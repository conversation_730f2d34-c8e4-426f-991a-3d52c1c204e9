import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import 'package:business_app/services/shop_service.dart';
import 'package:business_app/services/default_images_service.dart';
import 'package:business_app/services/storage_service.dart';
import 'product_event.dart';
import 'product_state.dart';

class ProductBloc extends Bloc<ProductEvent, ProductState> {
  static final Logger _logger = Logger();
  ProductBloc() : super(const ProductState()) {
    on<LoadProductsEvent>(_onLoadProducts);
    on<LoadMoreProductsEvent>(_onLoadMoreProducts);
    on<RefreshProductsEvent>(_onRefreshProducts);
    on<AddProductEvent>(_onAddProduct);
    on<UpdateProductEvent>(_onUpdateProduct);
    on<DeleteProductEvent>(_onDeleteProduct);
    on<ToggleProductAvailabilityEvent>(_onToggleProductAvailability);
    on<UpdateProductStockEvent>(_onUpdateProductStock);
    on<FilterProductsByCategoryEvent>(_onFilterProductsByCategory);
    on<SearchProductsEvent>(_onSearchProducts);
    on<ClearProductSearchEvent>(_onClearProductSearch);
    on<SortProductsEvent>(_onSortProducts);
    on<BulkDeleteProductsEvent>(_onBulkDeleteProducts);
    on<BulkUpdateProductAvailabilityEvent>(_onBulkUpdateProductAvailability);
    on<IncrementProductViewEvent>(_onIncrementProductView);
    on<LoadProductAnalyticsEvent>(_onLoadProductAnalytics);
  }

  Future<void> _onLoadProducts(
    LoadProductsEvent event,
    Emitter<ProductState> emit,
  ) async {
    if (event.refresh) {
      emit(state.copyWith(status: ProductStatus.refreshing));
    } else {
      emit(state.copyWith(status: ProductStatus.loading));
    }

    try {
      _logger.i('🔄 Loading products from Supabase...');

      // Get current user's shop first
      final userShop = await ShopService.getUserShop();
      if (userShop == null) {
        _logger.w(
          '⚠️ No shop found for current user, returning empty products list',
        );
        emit(
          state.copyWith(
            status: ProductStatus.loaded,
            products: [],
            hasReachedMax: true,
            clearErrorMessage: true,
          ),
        );
        return;
      }

      // Load products for the user's shop
      final products = await ShopService.getShopProducts(
        shopId: userShop.id,
        limit: 20,
        offset: 0,
      );

      _logger.i('✅ Loaded ${products.length} products from Supabase');

      // Add default images to products that don't have any
      final productsWithImages =
          products.map((product) {
            if (product.images.isEmpty) {
              final defaultImages = DefaultImagesService.generateProductImages(
                productName: product.name,
                categoryName: product.categoryName,
                imageCount: 2,
              );
              return product.copyWith(images: defaultImages);
            }
            return product;
          }).toList();

      emit(
        state.copyWith(
          status: ProductStatus.loaded,
          products: productsWithImages,
          hasReachedMax: products.length < 20,
          clearErrorMessage: true,
        ),
      );
    } catch (e) {
      _logger.e('❌ Failed to load products: $e');
      emit(
        state.copyWith(
          status: ProductStatus.error,
          errorMessage: 'Failed to load products: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoadMoreProducts(
    LoadMoreProductsEvent event,
    Emitter<ProductState> emit,
  ) async {
    if (state.hasReachedMax) return;

    emit(state.copyWith(status: ProductStatus.loadingMore));

    try {
      _logger.i('🔄 Loading more products from Supabase...');

      // Get current user's shop
      final userShop = await ShopService.getUserShop();
      if (userShop == null) {
        _logger.w('⚠️ No shop found for current user');
        emit(
          state.copyWith(
            status: ProductStatus.loaded,
            hasReachedMax: true,
            clearErrorMessage: true,
          ),
        );
        return;
      }

      // Load more products with offset
      final moreProducts = await ShopService.getShopProducts(
        shopId: userShop.id,
        limit: 20,
        offset: state.products.length,
      );

      _logger.i('✅ Loaded ${moreProducts.length} more products');

      // Add default images to new products that don't have any
      final moreProductsWithImages =
          moreProducts.map((product) {
            if (product.images.isEmpty) {
              final defaultImages = DefaultImagesService.generateProductImages(
                productName: product.name,
                categoryName: product.categoryName,
                imageCount: 2,
              );
              return product.copyWith(images: defaultImages);
            }
            return product;
          }).toList();

      final allProducts = [...state.products, ...moreProductsWithImages];

      emit(
        state.copyWith(
          status: ProductStatus.loaded,
          products: allProducts,
          hasReachedMax: moreProducts.length < 20,
          clearErrorMessage: true,
        ),
      );
    } catch (e) {
      _logger.e('❌ Failed to load more products: $e');
      emit(
        state.copyWith(
          status: ProductStatus.error,
          errorMessage: 'Failed to load more products: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onRefreshProducts(
    RefreshProductsEvent event,
    Emitter<ProductState> emit,
  ) async {
    add(const LoadProductsEvent(refresh: true));
  }

  Future<void> _onAddProduct(
    AddProductEvent event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(status: ProductStatus.adding));

    try {
      _logger.i('🔄 Adding product "${event.name}" to Supabase...');

      // Get current user's shop
      final userShop = await ShopService.getUserShop();
      if (userShop == null) {
        _logger.e('❌ No shop found for current user');
        emit(
          state.copyWith(
            status: ProductStatus.error,
            errorMessage: 'No shop found. Please create a shop first.',
          ),
        );
        return;
      }

      // Find category ID by name
      String? categoryId;
      try {
        final categories = await ShopService.getCategories();
        final matchingCategory = categories.firstWhere(
          (cat) => cat.name.toLowerCase() == event.category.toLowerCase(),
          orElse:
              () => categories.firstWhere(
                (cat) => cat.name.toLowerCase().contains('other'),
                orElse: () => categories.first,
              ),
        );
        categoryId = matchingCategory.id;
        _logger.i('✅ Found category ID: $categoryId for "${event.category}"');
      } catch (e) {
        _logger.w(
          '⚠️ Could not find category ID for "${event.category}", using fallback',
        );
        // Fallback to a simple transformation
        categoryId = event.category.toLowerCase().replaceAll(' ', '_');
      }

      // Prepare product images - use provided images or generate defaults
      List<String> productImages = List.from(event.images);

      // If no images provided, generate default images based on category
      if (productImages.isEmpty) {
        productImages = DefaultImagesService.generateProductImages(
          productName: event.name,
          categoryName: event.category,
          imageCount: 3, // Generate 3 default images
        );
        _logger.i(
          '📸 Generated ${productImages.length} default images for product',
        );
      }

      // Create product in Supabase
      final newProduct = await ShopService.createProduct(
        shopId: userShop.id,
        name: event.name,
        description: event.description,
        price: event.price,
        categoryId: categoryId,
        images: productImages,
        stockQuantity: event.stockQuantity,
        condition: 'new',
        brand: event.brand,
        tags: event.tags,
        specifications: event.specifications,
      );

      _logger.i('✅ Product "${event.name}" added successfully to Supabase');

      // Add to current products list
      final updatedProducts = [newProduct, ...state.products];

      emit(
        state.copyWith(
          status: ProductStatus.success,
          products: updatedProducts,
          successMessage: 'Product "${event.name}" added successfully!',
          clearErrorMessage: true,
        ),
      );
    } catch (e) {
      _logger.e('❌ Failed to add product: $e');
      emit(
        state.copyWith(
          status: ProductStatus.error,
          errorMessage: 'Failed to add product: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onUpdateProduct(
    UpdateProductEvent event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(status: ProductStatus.updating));

    try {
      await Future.delayed(const Duration(milliseconds: 800));

      final updatedProducts =
          state.products.map((product) {
            if (product.id == event.productId) {
              return product.copyWith(
                name: event.name,
                description: event.description,
                price: event.price,
                categoryName: event.category,
                images: event.images,
                stockQuantity: event.stockQuantity,
                status: (event.isAvailable ?? true) ? 'active' : 'inactive',
                discountPrice: event.discountPrice,
                brand: event.brand,
                weight: event.weight,
                dimensions:
                    event.dimensions != null
                        ? {'description': event.dimensions}
                        : null,
                tags: event.tags,
                specifications: event.specifications,
                updatedAt: DateTime.now(),
              );
            }
            return product;
          }).toList();

      emit(
        state.copyWith(
          status: ProductStatus.success,
          products: updatedProducts,
          successMessage: 'Product updated successfully!',
          clearErrorMessage: true,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: ProductStatus.error,
          errorMessage: 'Failed to update product: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onDeleteProduct(
    DeleteProductEvent event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(status: ProductStatus.deleting));

    try {
      _logger.i('🔄 Deleting product with ID: ${event.productId}');

      // Find the product to get its details (including images)
      final productToDelete = state.products.firstWhere(
        (product) => product.id == event.productId,
        orElse: () => throw Exception('Product not found'),
      );

      // Delete product images from storage first
      if (productToDelete.images.isNotEmpty) {
        _logger.i(
          '🗑️ Deleting ${productToDelete.images.length} product images...',
        );
        for (final imageUrl in productToDelete.images) {
          try {
            // Delete product image from storage
            final result = await StorageService.deleteProductImage(imageUrl);
            if (result.isSuccess) {
              _logger.i('✅ Deleted image: $imageUrl');
            } else {
              _logger.w('⚠️ Failed to delete image $imageUrl: ${result.error}');
            }
          } catch (imageError) {
            _logger.w('⚠️ Failed to delete image $imageUrl: $imageError');
            // Continue with product deletion even if image deletion fails
          }
        }
      }

      // Delete product from database
      await ShopService.deleteProduct(event.productId);
      _logger.i('✅ Product deleted from database');

      // Remove from local state
      final updatedProducts =
          state.products
              .where((product) => product.id != event.productId)
              .toList();

      emit(
        state.copyWith(
          status: ProductStatus.success,
          products: updatedProducts,
          successMessage:
              'Product "${productToDelete.name}" deleted successfully!',
          clearErrorMessage: true,
        ),
      );
    } catch (e) {
      _logger.e('❌ Failed to delete product: $e');
      emit(
        state.copyWith(
          status: ProductStatus.error,
          errorMessage: 'Failed to delete product: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onToggleProductAvailability(
    ToggleProductAvailabilityEvent event,
    Emitter<ProductState> emit,
  ) async {
    try {
      final updatedProducts =
          state.products.map((product) {
            if (product.id == event.productId) {
              return product.copyWith(
                status: product.isAvailable ? 'inactive' : 'active',
                updatedAt: DateTime.now(),
              );
            }
            return product;
          }).toList();

      emit(
        state.copyWith(
          status: ProductStatus.loaded,
          products: updatedProducts,
          successMessage: 'Product availability updated!',
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: ProductStatus.error,
          errorMessage:
              'Failed to update product availability: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onUpdateProductStock(
    UpdateProductStockEvent event,
    Emitter<ProductState> emit,
  ) async {
    try {
      final updatedProducts =
          state.products.map((product) {
            if (product.id == event.productId) {
              return product.copyWith(
                stockQuantity: event.newStock,
                updatedAt: DateTime.now(),
              );
            }
            return product;
          }).toList();

      emit(
        state.copyWith(
          status: ProductStatus.loaded,
          products: updatedProducts,
          successMessage: 'Stock updated successfully!',
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: ProductStatus.error,
          errorMessage: 'Failed to update stock: ${e.toString()}',
        ),
      );
    }
  }

  void _onFilterProductsByCategory(
    FilterProductsByCategoryEvent event,
    Emitter<ProductState> emit,
  ) {
    emit(
      state.copyWith(
        selectedCategory: event.category,
        clearSelectedCategory: event.category == null,
      ),
    );
  }

  void _onSearchProducts(
    SearchProductsEvent event,
    Emitter<ProductState> emit,
  ) {
    emit(state.copyWith(searchQuery: event.query));
  }

  void _onClearProductSearch(
    ClearProductSearchEvent event,
    Emitter<ProductState> emit,
  ) {
    emit(state.copyWith(searchQuery: ''));
  }

  void _onSortProducts(SortProductsEvent event, Emitter<ProductState> emit) {
    emit(state.copyWith(sortType: event.sortType));
  }

  Future<void> _onBulkDeleteProducts(
    BulkDeleteProductsEvent event,
    Emitter<ProductState> emit,
  ) async {
    emit(state.copyWith(status: ProductStatus.deleting));

    try {
      _logger.i('🔄 Bulk deleting ${event.productIds.length} products');

      // Find products to delete for image cleanup
      final productsToDelete =
          state.products
              .where((product) => event.productIds.contains(product.id))
              .toList();

      // Delete all product images from storage
      for (final product in productsToDelete) {
        if (product.images.isNotEmpty) {
          _logger.i(
            '🗑️ Deleting ${product.images.length} images for product: ${product.name}',
          );
          for (final imageUrl in product.images) {
            try {
              final result = await StorageService.deleteProductImage(imageUrl);
              if (result.isSuccess) {
                _logger.i('✅ Deleted image: $imageUrl');
              } else {
                _logger.w(
                  '⚠️ Failed to delete image $imageUrl: ${result.error}',
                );
              }
            } catch (imageError) {
              _logger.w('⚠️ Failed to delete image $imageUrl: $imageError');
              // Continue with deletion even if image deletion fails
            }
          }
        }
      }

      // Delete products from database
      for (final productId in event.productIds) {
        try {
          await ShopService.deleteProduct(productId);
          _logger.i('✅ Product $productId deleted from database');
        } catch (e) {
          _logger.e('❌ Failed to delete product $productId from database: $e');
          throw Exception('Failed to delete product $productId: $e');
        }
      }

      // Remove from local state
      final updatedProducts =
          state.products
              .where((product) => !event.productIds.contains(product.id))
              .toList();

      emit(
        state.copyWith(
          status: ProductStatus.success,
          products: updatedProducts,
          successMessage:
              '${event.productIds.length} products deleted successfully!',
          isSelectionMode: false,
          selectedProductIds: const {},
          clearErrorMessage: true,
        ),
      );
    } catch (e) {
      _logger.e('❌ Failed to bulk delete products: $e');
      emit(
        state.copyWith(
          status: ProductStatus.error,
          errorMessage: 'Failed to delete products: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onBulkUpdateProductAvailability(
    BulkUpdateProductAvailabilityEvent event,
    Emitter<ProductState> emit,
  ) async {
    try {
      final updatedProducts =
          state.products.map((product) {
            if (event.productIds.contains(product.id)) {
              return product.copyWith(
                status: event.isAvailable ? 'active' : 'inactive',
                updatedAt: DateTime.now(),
              );
            }
            return product;
          }).toList();

      emit(
        state.copyWith(
          status: ProductStatus.loaded,
          products: updatedProducts,
          successMessage:
              '${event.productIds.length} products updated successfully!',
          isSelectionMode: false,
          selectedProductIds: const {},
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: ProductStatus.error,
          errorMessage: 'Failed to update products: ${e.toString()}',
        ),
      );
    }
  }

  void _onIncrementProductView(
    IncrementProductViewEvent event,
    Emitter<ProductState> emit,
  ) {
    final updatedProducts =
        state.products.map((product) {
          if (product.id == event.productId) {
            return product.copyWith(viewCount: product.viewCount + 1);
          }
          return product;
        }).toList();

    emit(state.copyWith(products: updatedProducts));
  }

  Future<void> _onLoadProductAnalytics(
    LoadProductAnalyticsEvent event,
    Emitter<ProductState> emit,
  ) async {
    // TODO: Implementation for loading product analytics from Supabase
    _logger.i('📊 Loading product analytics...');
    // This would typically fetch analytics data from backend
  }
}
