import 'dart:io';
import 'package:business_app/bloc/product_bloc/product_bloc.dart';
import 'package:business_app/bloc/product_bloc/product_event.dart';
import 'package:business_app/bloc/product_bloc/product_state.dart';
import 'package:business_app/models/category/product_category.dart';
import 'package:business_app/services/storage_service.dart';
import 'package:business_app/services/supabase_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

class AddProductPage extends StatefulWidget {
  const AddProductPage({super.key});

  @override
  State<AddProductPage> createState() => _AddProductPageState();
}

class _AddProductPageState extends State<AddProductPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _discountPriceController = TextEditingController();
  final _stockController = TextEditingController();
  final _brandController = TextEditingController();
  final _weightController = TextEditingController();
  final _dimensionsController = TextEditingController();
  final _tagsController = TextEditingController();

  String? _selectedCategory;
  String? _selectedImage; // Changed to single image
  final ImagePicker _imagePicker = ImagePicker();

  // Image upload state
  bool _isUploadingImages = false;
  // Track progress for each image

  // Character limits
  static const int nameLimit = 100;
  static const int descriptionLimit = 500;
  static const int brandLimit = 50;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _discountPriceController.dispose();
    _stockController.dispose();
    _brandController.dispose();
    _weightController.dispose();
    _dimensionsController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Product'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors:
                  isDark
                      ? [const Color(0xFF1A1A2E), const Color(0xFF16213E)]
                      : [const Color(0xFF667eea), const Color(0xFF764ba2)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        foregroundColor: Colors.white,
        actions: [
          BlocBuilder<ProductBloc, ProductState>(
            builder: (context, state) {
              return TextButton(
                onPressed:
                    state.status == ProductStatus.adding ? null : _saveProduct,
                child: Text(
                  'Save',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: BlocListener<ProductBloc, ProductState>(
        listener: (context, state) {
          if (state.status == ProductStatus.success &&
              state.successMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.successMessage!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                backgroundColor: Colors.blue,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: const Duration(milliseconds: 800),
              ),
            );
            Navigator.pop(context);
          }

          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                duration: const Duration(milliseconds: 800),
              ),
            );
          }
        },
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16),
            physics: const BouncingScrollPhysics(),
            children: [
              // Product Images Section
              _buildImageSection(),
              const SizedBox(height: 24),

              // Basic Information Section
              _buildSectionHeader('Basic Information'),
              const SizedBox(height: 16),
              _buildNameField(),
              const SizedBox(height: 16),
              _buildDescriptionField(),
              const SizedBox(height: 16),
              _buildCategoryField(),
              const SizedBox(height: 24),

              // Pricing Section
              _buildSectionHeader('Pricing'),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(child: _buildPriceField()),
                  const SizedBox(width: 16),
                  Expanded(child: _buildDiscountPriceField()),
                ],
              ),
              const SizedBox(height: 24),

              // Inventory Section
              _buildSectionHeader('Inventory'),
              const SizedBox(height: 16),
              _buildStockField(),
              const SizedBox(height: 24),

              // Additional Details Section
              _buildSectionHeader('Additional Details'),
              const SizedBox(height: 16),
              _buildBrandField(),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(child: _buildWeightField()),
                  const SizedBox(width: 16),
                  Expanded(child: _buildDimensionsField()),
                ],
              ),
              const SizedBox(height: 16),
              _buildTagsField(),
              const SizedBox(height: 32),

              // Save Button
              BlocBuilder<ProductBloc, ProductState>(
                builder: (context, state) {
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      gradient: const LinearGradient(
                        colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667eea).withValues(alpha: 0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 6),
                        ),
                      ],
                    ),
                    child: ElevatedButton(
                      onPressed:
                          state.status == ProductStatus.adding
                              ? null
                              : _saveProduct,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        shadowColor: Colors.transparent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child:
                          state.status == ProductStatus.adding
                              ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : const Text(
                                'Add Product',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildImageSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.photo_library, color: Color(0xFF667eea)),
              const SizedBox(width: 8),
              const Text(
                'Product Image *',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: _isUploadingImages ? null : _pickImage,
                icon:
                    _isUploadingImages
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.add_photo_alternate),
                label: Text(
                  _isUploadingImages
                      ? 'Uploading...'
                      : _selectedImage == null
                      ? 'Add Image'
                      : 'Change Image',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (_selectedImage == null)
            GestureDetector(
              onTap: _isUploadingImages ? null : _pickImage,
              child: Container(
                height: 120,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.grey[300]!,
                    style: BorderStyle.solid,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add_photo_alternate,
                      size: 40,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tap to add product image *',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            )
          else
            Center(
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      _selectedImage!,
                      width: 200,
                      height: 200,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          width: 200,
                          height: 200,
                          color: Colors.grey[200],
                          child: Center(
                            child: CircularProgressIndicator(
                              value:
                                  loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                              strokeWidth: 2,
                            ),
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 200,
                          height: 200,
                          color: Colors.grey[300],
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error, color: Colors.red),
                              const SizedBox(height: 4),
                              Text(
                                'Failed to load',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: _removeImage,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 18,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: InputDecoration(
        labelText: 'Product Name *',
        hintText: 'Enter product name',
        prefixIcon: const Icon(Icons.shopping_bag),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        counterText: '${_nameController.text.length}/$nameLimit',
      ),
      maxLength: nameLimit,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Product name is required';
        }
        if (value.trim().length < 3) {
          return 'Product name must be at least 3 characters';
        }
        return null;
      },
      onChanged: (value) => setState(() {}),
    );
  }

  Widget _buildDescriptionField() {
    return TextFormField(
      controller: _descriptionController,
      decoration: InputDecoration(
        labelText: 'Description *',
        hintText: 'Describe your product in detail',
        prefixIcon: const Icon(Icons.description),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        counterText: '${_descriptionController.text.length}/$descriptionLimit',
      ),
      maxLines: 4,
      maxLength: descriptionLimit,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Description is required';
        }
        if (value.trim().length < 10) {
          return 'Description must be at least 10 characters';
        }
        return null;
      },
      onChanged: (value) => setState(() {}),
    );
  }

  Widget _buildCategoryField() {
    final categories = CategoryData.getDefaultCategories();

    return DropdownButtonFormField<String>(
      value: _selectedCategory,
      decoration: InputDecoration(
        labelText: 'Category *',
        prefixIcon: const Icon(Icons.category),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      items:
          categories.map((category) {
            return DropdownMenuItem(
              value: category.name,
              child: Text(category.name),
            );
          }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedCategory = value;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please select a category';
        }
        return null;
      },
    );
  }

  Widget _buildPriceField() {
    return TextFormField(
      controller: _priceController,
      decoration: InputDecoration(
        labelText: 'Price *',
        hintText: '0.00',
        prefixIcon: const Icon(Icons.attach_money),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
      ],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Price is required';
        }
        final price = double.tryParse(value);
        if (price == null || price <= 0) {
          return 'Enter a valid price';
        }
        return null;
      },
    );
  }

  Widget _buildDiscountPriceField() {
    return TextFormField(
      controller: _discountPriceController,
      decoration: InputDecoration(
        labelText: 'Discount Price',
        hintText: '0.00',
        prefixIcon: const Icon(Icons.local_offer),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
      ],
      validator: (value) {
        if (value != null && value.isNotEmpty) {
          final discountPrice = double.tryParse(value);
          final originalPrice = double.tryParse(_priceController.text);

          if (discountPrice == null || discountPrice <= 0) {
            return 'Enter a valid discount price';
          }

          if (originalPrice != null && discountPrice >= originalPrice) {
            return 'Discount price must be less than original price';
          }
        }
        return null;
      },
    );
  }

  Widget _buildStockField() {
    return TextFormField(
      controller: _stockController,
      decoration: InputDecoration(
        labelText: 'Stock Quantity *',
        hintText: '0',
        prefixIcon: const Icon(Icons.inventory),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Stock quantity is required';
        }
        final stock = int.tryParse(value);
        if (stock == null || stock < 0) {
          return 'Enter a valid stock quantity';
        }
        return null;
      },
    );
  }

  Widget _buildBrandField() {
    return TextFormField(
      controller: _brandController,
      decoration: InputDecoration(
        labelText: 'Brand',
        hintText: 'Enter brand name',
        prefixIcon: const Icon(Icons.branding_watermark),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        counterText: '${_brandController.text.length}/$brandLimit',
      ),
      maxLength: brandLimit,
      onChanged: (value) => setState(() {}),
    );
  }

  Widget _buildWeightField() {
    return TextFormField(
      controller: _weightController,
      decoration: InputDecoration(
        labelText: 'Weight (kg)',
        hintText: '0.0',
        prefixIcon: const Icon(Icons.scale),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
      ],
    );
  }

  Widget _buildDimensionsField() {
    return TextFormField(
      controller: _dimensionsController,
      decoration: InputDecoration(
        labelText: 'Dimensions',
        hintText: 'L x W x H',
        prefixIcon: const Icon(Icons.straighten),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Widget _buildTagsField() {
    return TextFormField(
      controller: _tagsController,
      decoration: InputDecoration(
        labelText: 'Tags',
        hintText: 'Enter tags separated by commas',
        prefixIcon: const Icon(Icons.tag),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        helperText:
            'Separate tags with commas (e.g., electronics, gadget, wireless)',
      ),
    );
  }

  void _pickImage() async {
    try {
      // Check if already uploading
      if (_isUploadingImages) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please wait for current upload to complete'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Show source selection dialog
      final ImageSource? source = await showDialog<ImageSource>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Select Image Source'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Gallery'),
                  onTap: () => Navigator.of(context).pop(ImageSource.gallery),
                ),
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Camera'),
                  onTap: () => Navigator.of(context).pop(ImageSource.camera),
                ),
              ],
            ),
          );
        },
      );

      if (source == null) return;

      final XFile? image = await _imagePicker.pickImage(source: source);
      if (image == null) return;

      // Get current user ID
      final userId = BaseSupabaseService.client.auth.currentUser?.id;
      if (userId == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please log in to upload image'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      setState(() {
        _isUploadingImages = true;
      });

      // Show upload progress dialog
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => AlertDialog(
                title: const Text('Uploading Image'),
                content: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Uploading image...'),
                  ],
                ),
              ),
        );
      }

      // Upload the image
      try {
        final imageFile = File(image.path);
        final result = await StorageService.uploadProductImage(
          userId: userId,
          imageFile: imageFile,
        );

        if (result.isSuccess && result.data != null) {
          // Close progress dialog
          if (mounted) Navigator.of(context).pop();

          // Set the uploaded image URL
          setState(() {
            _selectedImage = result.data!;
            _isUploadingImages = false;
          });

          // Show success message
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Successfully uploaded image'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          throw Exception(result.error ?? 'Upload failed');
        }
      } catch (e) {
        // Close progress dialog
        if (mounted) Navigator.of(context).pop();

        setState(() {
          _isUploadingImages = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to upload image: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Ensure we clean up state and close any dialogs
      if (mounted) {
        Navigator.of(
          context,
          rootNavigator: true,
        ).popUntil((route) => route.isFirst);
      }

      setState(() {
        _isUploadingImages = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking/uploading image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage() {
    setState(() {
      _selectedImage = null;
    });
  }

  void _saveProduct() {
    if (_formKey.currentState!.validate()) {
      // Validate that an image is selected
      if (_selectedImage == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please upload a product image'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
        return;
      }

      final tags =
          _tagsController.text
              .split(',')
              .map((tag) => tag.trim())
              .where((tag) => tag.isNotEmpty)
              .toList();

      context.read<ProductBloc>().add(
        AddProductEvent(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim(),
          price: double.parse(_priceController.text),
          category: _selectedCategory!,
          images: [_selectedImage!], // Convert single image to list
          stockQuantity: int.parse(_stockController.text),
          discountPrice:
              _discountPriceController.text.isNotEmpty
                  ? double.parse(_discountPriceController.text)
                  : null,
          brand:
              _brandController.text.trim().isNotEmpty
                  ? _brandController.text.trim()
                  : null,
          weight:
              _weightController.text.isNotEmpty
                  ? double.parse(_weightController.text)
                  : null,
          dimensions:
              _dimensionsController.text.trim().isNotEmpty
                  ? _dimensionsController.text.trim()
                  : null,
          tags: tags,
        ),
      );
    }
  }
}
